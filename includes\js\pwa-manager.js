/**
 * PWA Manager - Handles Progressive Web App functionality
 */

class QPWAManager {
    constructor() {
        this.settings = window.qPWASettings || {};
        this.deferredPrompt = null;
        this.isInstalled = false;
        
        this.init();
    }

    init() {
        if (!this.settings.enabled) {
            console.log('Q-PWA: PWA functionality is disabled');
            return;
        }

        console.log('Q-PWA: Initializing PWA Manager');
        
        this.checkInstallation();
        this.setupInstallPrompt();
        this.setupServiceWorker();
        this.setupOfflineHandling();
        this.setupAppShell();
        this.addInstallButton();
    }

    checkInstallation() {
        // Check if app is already installed
        if (window.matchMedia('(display-mode: standalone)').matches) {
            this.isInstalled = true;
            console.log('Q-PWA: App is running in standalone mode');
            document.body.classList.add('pwa-installed');
        }

        // Check for iOS standalone mode
        if (window.navigator.standalone === true) {
            this.isInstalled = true;
            console.log('Q-PWA: App is running in iOS standalone mode');
            document.body.classList.add('pwa-installed');
        }
    }

    setupInstallPrompt() {
        // Listen for the beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('Q-PWA: Install prompt available');
            
            // Prevent the mini-infobar from appearing on mobile
            e.preventDefault();
            
            // Save the event so it can be triggered later
            this.deferredPrompt = e;
            
            // Show custom install button
            this.showInstallButton();
        });

        // Listen for app installation
        window.addEventListener('appinstalled', (e) => {
            console.log('Q-PWA: App was installed');
            this.isInstalled = true;
            this.hideInstallButton();
            this.deferredPrompt = null;
            
            // Track installation
            this.trackEvent('pwa_installed');
        });
    }

    setupServiceWorker() {
        if (!('serviceWorker' in navigator)) {
            console.log('Q-PWA: Service workers not supported');
            return;
        }

        // Register service worker
        navigator.serviceWorker.register('/firebase-messaging-sw.js', {
            scope: '/'
        })
        .then((registration) => {
            console.log('Q-PWA: Service worker registered successfully');
            
            // Listen for service worker updates
            registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                
                newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                        // New content is available
                        this.showUpdateNotification();
                    }
                });
            });
        })
        .catch((error) => {
            console.error('Q-PWA: Service worker registration failed:', error);
        });

        // Listen for service worker messages
        navigator.serviceWorker.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'NOTIFICATION_CLICKED') {
                console.log('Q-PWA: Notification clicked', event.data);
                this.handleNotificationClick(event.data);
            }
        });
    }

    setupOfflineHandling() {
        if (!this.settings.offlineEnabled) {
            return;
        }

        // Listen for online/offline events
        window.addEventListener('online', () => {
            console.log('Q-PWA: Back online');
            this.hideOfflineNotification();
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            console.log('Q-PWA: Gone offline');
            this.showOfflineNotification();
        });

        // Check initial connection status
        if (!navigator.onLine) {
            this.showOfflineNotification();
        }
    }

    setupAppShell() {
        // Add PWA-specific classes to body
        document.body.classList.add('pwa-enabled');
        
        // Add viewport meta tag if not present
        if (!document.querySelector('meta[name="viewport"]')) {
            const viewport = document.createElement('meta');
            viewport.name = 'viewport';
            viewport.content = 'width=device-width, initial-scale=1, shrink-to-fit=no';
            document.head.appendChild(viewport);
        }

        // Prevent zoom on iOS
        document.addEventListener('gesturestart', (e) => {
            e.preventDefault();
        });

        // Handle iOS status bar
        if (this.isInstalled && window.navigator.standalone) {
            document.body.classList.add('ios-standalone');
        }
    }

    addInstallButton() {
        // Create install button container
        const installContainer = document.createElement('div');
        installContainer.id = 'q-pwa-install-container';
        installContainer.className = 'q-pwa-install-container hidden';
        
        installContainer.innerHTML = `
            <div class="q-pwa-install-prompt">
                <div class="q-pwa-install-content">
                    <div class="q-pwa-install-icon">📱</div>
                    <div class="q-pwa-install-text">
                        <h3>Install ${this.settings.appName || 'App'}</h3>
                        <p>Get the full app experience with offline access and push notifications.</p>
                    </div>
                    <div class="q-pwa-install-actions">
                        <button id="q-pwa-install-btn" class="q-pwa-btn-primary">Install</button>
                        <button id="q-pwa-install-dismiss" class="q-pwa-btn-secondary">Not now</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(installContainer);

        // Add event listeners
        document.getElementById('q-pwa-install-btn').addEventListener('click', () => {
            this.promptInstall();
        });

        document.getElementById('q-pwa-install-dismiss').addEventListener('click', () => {
            this.hideInstallButton();
            this.trackEvent('install_prompt_dismissed');
        });
    }

    showInstallButton() {
        const container = document.getElementById('q-pwa-install-container');
        if (container && !this.isInstalled) {
            container.classList.remove('hidden');
            this.trackEvent('install_prompt_shown');
        }
    }

    hideInstallButton() {
        const container = document.getElementById('q-pwa-install-container');
        if (container) {
            container.classList.add('hidden');
        }
    }

    promptInstall() {
        if (!this.deferredPrompt) {
            console.log('Q-PWA: No install prompt available');
            return;
        }

        // Show the install prompt
        this.deferredPrompt.prompt();

        // Wait for the user to respond to the prompt
        this.deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                console.log('Q-PWA: User accepted the install prompt');
                this.trackEvent('install_accepted');
            } else {
                console.log('Q-PWA: User dismissed the install prompt');
                this.trackEvent('install_dismissed');
            }
            
            this.deferredPrompt = null;
            this.hideInstallButton();
        });
    }

    showUpdateNotification() {
        // Create update notification
        const notification = document.createElement('div');
        notification.id = 'q-pwa-update-notification';
        notification.className = 'q-pwa-notification';
        notification.innerHTML = `
            <div class="q-pwa-notification-content">
                <span>🔄 New version available!</span>
                <button id="q-pwa-update-btn">Update</button>
                <button id="q-pwa-update-dismiss">×</button>
            </div>
        `;

        document.body.appendChild(notification);

        // Add event listeners
        document.getElementById('q-pwa-update-btn').addEventListener('click', () => {
            window.location.reload();
        });

        document.getElementById('q-pwa-update-dismiss').addEventListener('click', () => {
            notification.remove();
        });

        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);
    }

    showOfflineNotification() {
        if (document.getElementById('q-pwa-offline-notification')) {
            return; // Already showing
        }

        const notification = document.createElement('div');
        notification.id = 'q-pwa-offline-notification';
        notification.className = 'q-pwa-notification offline';
        notification.innerHTML = `
            <div class="q-pwa-notification-content">
                <span>📡 You're offline. Some features may be limited.</span>
            </div>
        `;

        document.body.appendChild(notification);
    }

    hideOfflineNotification() {
        const notification = document.getElementById('q-pwa-offline-notification');
        if (notification) {
            notification.remove();
        }
    }

    syncOfflineData() {
        // Implement offline data synchronization
        console.log('Q-PWA: Syncing offline data...');

        // Sync offline events
        this.syncOfflineEvents();

        // This would typically sync any cached form submissions,
        // user actions, or other data that was stored while offline
    }

    syncOfflineEvents() {
        try {
            const offlineEvents = JSON.parse(localStorage.getItem('q_pwa_offline_events') || '[]');

            if (offlineEvents.length === 0) {
                return;
            }

            console.log(`Q-PWA: Syncing ${offlineEvents.length} offline events`);

            // Send each event
            offlineEvents.forEach((eventData, index) => {
                if (this.settings.ajaxUrl && this.settings.nonce) {
                    jQuery.post(this.settings.ajaxUrl, {
                        action: 'q_track_pwa_event',
                        event: eventData.event,
                        data: eventData.data,
                        timestamp: eventData.timestamp,
                        nonce: this.settings.nonce
                    }).done(() => {
                        console.log('Q-PWA: Offline event synced:', eventData.event);
                    }).fail((xhr, status, error) => {
                        console.error('Q-PWA: Failed to sync offline event:', error);
                    });
                }
            });

            // Clear synced events
            localStorage.removeItem('q_pwa_offline_events');
            console.log('Q-PWA: Offline events cleared from storage');

        } catch (error) {
            console.error('Q-PWA: Failed to sync offline events:', error);
        }
    }

    handleNotificationClick(data) {
        // Handle notification click events
        console.log('Q-PWA: Handling notification click', data);
        
        // You can implement custom logic here based on the notification data
        if (data.notification && data.notification.id) {
            this.trackEvent('notification_clicked', {
                notification_id: data.notification.id
            });
        }
    }

    trackEvent(eventName, data = {}) {
        // Track PWA events for analytics
        console.log('Q-PWA Event:', eventName, data);

        // Send to analytics if available
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, {
                event_category: 'PWA',
                ...data
            });
        }

        // Send to WordPress via AJAX only if online
        if (this.settings.ajaxUrl && this.settings.nonce && navigator.onLine) {
            jQuery.post(this.settings.ajaxUrl, {
                action: 'q_track_pwa_event',
                event: eventName,
                data: data,
                nonce: this.settings.nonce
            }).fail((xhr, status, error) => {
                console.log('Q-PWA: Failed to track event:', error);
                // Store event for later sync when back online
                this.storeOfflineEvent(eventName, data);
            });
        } else if (!navigator.onLine) {
            // Store event for later sync when back online
            this.storeOfflineEvent(eventName, data);
        }
    }

    storeOfflineEvent(eventName, data) {
        // Store events in localStorage for sync when back online
        try {
            const offlineEvents = JSON.parse(localStorage.getItem('q_pwa_offline_events') || '[]');
            offlineEvents.push({
                event: eventName,
                data: data,
                timestamp: Date.now()
            });
            localStorage.setItem('q_pwa_offline_events', JSON.stringify(offlineEvents));
            console.log('Q-PWA: Event stored for offline sync:', eventName);
        } catch (error) {
            console.error('Q-PWA: Failed to store offline event:', error);
        }
    }
}

// Initialize PWA Manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.qPWAManager = new QPWAManager();
});

// Export for use in other scripts
window.QPWAManager = QPWAManager;
